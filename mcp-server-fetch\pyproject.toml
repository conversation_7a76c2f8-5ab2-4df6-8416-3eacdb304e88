[project]
name = "mcp-server-fetch"
version = "0.6.2"
description = "A Model Context Protocol server providing tools to fetch and convert web content for usage by LLMs"
readme = "README.md"
requires-python = ">=3.12"
authors = [{ name = "Anthropic, PBC." }]
maintainers = [{ name = "<PERSON><PERSON><PERSON> Smeets", email = "<EMAIL>" }]
keywords = ["http", "mcp", "llm", "automation"]
license = { text = "MIT" }
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "setuptools>=68.0.0",
    "fastapi==0.100.1",
    "uvicorn==0.34.0",
    "pillow>=10.2.0",
    "pytesseract>=0.3.10",
    "requests>=2.32.3",
    "beautifulsoup4>=4.12.3",
    "numpy>=1.26.0",
    "selenium>=4.18.0",
    "undetected-chromedriver>=3.5.5",
    "layoutparser>=0.3.4",
    "torch==2.2.0+cpu",
    "torchvision==0.17.0+cpu",
    "torchaudio==2.2.0+cpu",
    "pydantic>=2.0.0",
    "pydantic-settings==2.8.1",
    "markdownify>=0.13.1",
    "mcp>=1.1.3",
    "protego>=0.3.1",
    "readabilipy>=0.2.0",
    "httpx>=0.27.0",
    "python-docx>=0.8.11",
    "PyPDF2>=3.0.0",
    "python-pptx>=0.6.21"
]

[project.urls]
Homepage = "https://github.com/MaartenSmeets/mcp-server-fetch"
Bug-Tracker = "https://github.com/MaartenSmeets/mcp-server-fetch/issues"

[project.scripts]
mcp-server-fetch = "mcp_server_fetch:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = ["pyright>=1.1.389", "ruff>=0.7.3"]

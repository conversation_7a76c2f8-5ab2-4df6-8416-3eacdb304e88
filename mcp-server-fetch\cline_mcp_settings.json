{"mcpServers": {"fetch": {"command": "docker", "args": ["run", "--rm", "-i", "mcp-server-fetch"], "disabled": false, "alwaysAllow": []}, "github.com/Saik0s/mcp-browser-use": {"command": "uvx", "args": ["mcp-server-browser-use@latest"], "disabled": false, "alwaysAllow": [], "env": {"MCP_BROWSER_HEADLESS": "true"}}, "github.com/executeautomation/mcp-playwright": {"command": "npx", "args": ["@executeautomation/playwright-mcp-server"], "disabled": false, "alwaysAllow": []}}}
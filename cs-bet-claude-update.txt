# CS2 Match Analysis & Smart Betting Predictor

## Objective
Analyze any CS2 match URL and provide intelligent betting predictions using comprehensive data extraction and advanced analytical logic.

## Instructions

### 1. Dynamic URL Processing
**Use the tool playwright_navigate** to scrape the provided CS2 match URL dynamically. The system should work for any valid match URL format, including but not limited to:
- ensigame.com match pages
- Other CS2 esports platforms
- Analyze this MatchURL: https://ensigame.com/matches/cs-2/1356679-sinners-esports-sinners-vs-favbet-team-favbet-esea-aeu-

#### Team-Level Metrics:
- **Overall Team Rating**: ENSI.Rank, ENSI.Score, HLTV Rating, or equivalent
- **Win/Loss Records**: Last 10, 30, and overall matches
- **Current Form**: Recent streak, momentum indicators
- **Map Pool Analysis**: Win rates per map, preferred/banned maps
- **Team Chemistry Indicators**: Average team K/D, ADR, KAST%
- **Tournament Performance**: Results in current/recent tournaments
- **Head-to-Head History**: Direct matchup record and recent encounters

#### Player-Level Metrics:
- **Individual Ratings**: Last 10 matches K/D, ADR, KAST%, Impact Rating
- **Role Performance**: Entry fraggers, support players, IGL effectiveness
- **Consistency Metrics**: Performance variance, clutch success rates
- **Map-Specific Performance**: Player stats on likely map picks
- **Recent Activity**: Games played, rest periods, roster changes

#### Match Context:
- **Tournament Importance**: Stakes, prize pool, qualification implications
- **Schedule Analysis**: Recent match frequency, travel/fatigue factors
- **External Factors**: Roster changes, coach changes, recent controversies

### 3. Advanced Betting Logic Framework

#### Statistical Analysis:
1. **Weighted Performance Scoring**: Recent matches weighted more heavily than historical data
2. **Map Advantage Calculation**: Compare team win rates on likely map pool
3. **Momentum Analysis**: Identify teams on upward/downward trajectories
4. **Variance Assessment**: Account for consistency vs. volatility in performance

#### Multi-Layered Prediction Model:
1. **Base Probability**: Raw win percentage based on historical data
2. **Form Adjustment**: Modify based on recent 5-10 match performance
3. **Map Pool Consideration**: Adjust for specific map advantages/disadvantages
4. **Context Weighting**: Factor in tournament importance and external circumstances
5. **Market Inefficiency Detection**: Identify value bets where odds don't match analysis

#### Smart Betting Types Analysis:
- **Match Winner**: Straight win/loss prediction with confidence intervals
- **Map Handicap**: +/- maps based on relative team strength
- **Total Maps**: Over/Under based on team styles (quick closers vs. grinders)
- **Round Handicap**: +/- rounds on individual maps
- **Special Bets**: First blood, pistol round winners, specific player performance

### 4. Risk Assessment & Bankroll Management
- **Confidence Tiers**: High (8-10/10), Medium (5-7/10), Low (3-4/10)
- **Recommended Stake**: Percentage of bankroll based on confidence and edge
- **Multiple Bet Combinations**: Stack complementary bets when appropriate
- **Hedge Opportunities**: Identify situations for risk mitigation

### 5. Structured Output Format

```
## MATCH ANALYSIS REPORT

### Match Details
- **Teams**: [Team A] vs [Team B]
- **Tournament**: [Tournament Name]
- **Date/Time**: [Match Date and Time]
- **Format**: [BO1/BO3/BO5]
- **Map Pool**: [Expected maps]

### Team Comparison Matrix
| Metric | Team A | Team B | Advantage |
|--------|--------|---------|-----------|
| Overall Rating | X.XX | X.XX | Team X (+X.XX) |
| Recent Form (L10) | X-X | X-X | Team X |
| Map Pool Strength | XX% | XX% | Team X (+XX%) |
| H2H Record | X-X | X-X | Team X |

### Key Player Matchups
- **Star Player Duel**: [Player A] vs [Player B] - Key impact analysis
- **Support Role Analysis**: Comparison of secondary fraggers
- **IGL Factor**: Leadership and tactical advantage assessment

### Advanced Predictions

#### Primary Bet Recommendation
- **Bet Type**: [Match Winner/Handicap/Total Maps]
- **Selection**: [Specific bet selection]
- **Confidence**: X/10
- **Expected Value**: +X% (positive edge identified)
- **Recommended Stake**: X% of bankroll

#### Secondary Opportunities
1. **Bet Type 2**: [Selection] - Confidence: X/10
2. **Bet Type 3**: [Selection] - Confidence: X/10

### Risk Factors & Contingencies
- **Potential Upsets**: Factors that could swing the match
- **Map Volatility**: Uncertainty in map selection impact
- **External Variables**: Roster/health/motivation concerns